import React, { useState } from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { GlobalContext } from "Context/Global";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { PaginationBar } from "Components/PaginationBar";
import _ from "lodash";
import SkeletonLoader from "Components/Skeleton/Skeleton";
import { Link } from "react-router-dom";
import { fDate, fTimeSuffix } from "Utils/formatTime";
import { fCurrency } from "Utils/formatNumber";
import LoadingOverlay from "Components/Loading/LoadingOverlay";
import {
  BOOKING_STATUSES,
  eventTypeOptions,
  calculateReservationTimeLeft,
} from "Utils/utils";
import TreeSDK from "Utils/TreeSDK";
import {
  FailedStatus,
  GroupFullStatus,
  LookingForBuddiesStatus,
  PaidStatus,
  ReservedStatus,
  TimeStatus,
} from "Components/ReservationStatus";
import ReservationDetailModal from "Components/ReservationModals";
import Select from "react-select";

let sdk = new MkdSDK();
let tdk = new TreeSDK();

const columns = [
  // {
  //   header: "Id",
  //   accessor: "id",
  //   isSorted: false,
  //   isSortedDesc: false,
  //   mappingExist: false,
  //   mappings: {},
  // },

  {
    header: "Date & Time",
    accessor: "date",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Booking Type",
    accessor: "booking_type",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Players",
    accessor: "players",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
  },

  {
    header: "Price",
    accessor: "price",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Status",
    accessor: "reservation_status",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: true,
    mappings: { 1: "Active", 0: "Inactive" },
  },
  //   {
  //     header: "Action",
  //     accessor: "",
  //   },
];

const UserMyReservation = () => {
  const { dispatch: globalDispatch, state: globalState } =
    React.useContext(GlobalContext);
  const { dispatch } = React.useContext(AuthContext);
  const [data, setCurrentTableData] = React.useState([]);
  const [pageSize, setPageSize] = React.useState(10);
  const [pageCount, setPageCount] = React.useState(0);
  const [dataTotal, setDataTotal] = React.useState(0);
  const [currentPage, setPage] = React.useState(0);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);
  const [openFilter, setOpenFilter] = React.useState(false);
  const [showFilterOptions, setShowFilterOptions] = React.useState(false);
  const [selectedOptions, setSelectedOptions] = React.useState([]);
  const [filterConditions, setFilterConditions] = React.useState([]);
  const [optionValue, setOptionValue] = React.useState("eq");
  const [loading, setLoading] = React.useState(true);
  const [showEditSidebar, setShowEditSidebar] = React.useState(false);
  const [showAddSidebar, setShowAddSidebar] = React.useState(false);
  const [activeEditId, setActiveEditId] = React.useState();
  const navigate = useNavigate();
  const dropdownFilterRef = React.useRef(null);
  const [showDetailsModal, setShowDetailsModal] = React.useState(false);
  const [selectedReservation, setSelectedReservation] = React.useState(null);
  const [userList, setUserList] = React.useState([]);
  const [showAddDrawer, setShowAddDrawer] = useState(false);
  const [sports, setSports] = useState([]);
  const [surfaces, setSurfaces] = useState([]);
  const [players, setPlayers] = useState([]);
  const [activeTab, setActiveTab] = useState("upcoming");
  const [showFindBuddyRequest, setShowFindBuddyRequest] = useState(false);
  const [showReservationDetails, setShowReservationDetails] = useState(false);
  const [club, setClub] = useState(null);
  const [familyMembers, setFamilyMembers] = useState([]);
  const [selectedFamilyMember, setSelectedFamilyMember] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const tabs = [
    {
      id: "upcoming",
      label: "Upcoming",
      icon: () => (
        <svg
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M2.05153 8.29769L3.13684 14.4527C3.21675 14.906 3.64897 15.2086 4.10222 15.1287L8.42993 14.3656M2.05153 8.29769L1.61741 5.83567C1.53749 5.38242 1.84013 4.95021 2.29338 4.87029L11.7311 3.20616C12.1844 3.12624 12.6166 3.42888 12.6965 3.88213L13.1306 6.34414L2.05153 8.29769ZM13.3333 9.79243V11.6674L15 13.3341M18.5417 11.6674C18.5417 14.5439 16.2098 16.8758 13.3333 16.8758C10.4569 16.8758 8.125 14.5439 8.125 11.6674C8.125 8.79095 10.4569 6.4591 13.3333 6.4591C16.2098 6.4591 18.5417 8.79095 18.5417 11.6674Z"
            stroke="black"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      ),
    },
    {
      id: "past",
      label: "Past",
      icon: () => (
        <svg
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect width="20" height="20" fill="white" />
          <path
            d="M12.5 7.91602L8.75001 12.4993L7.08334 10.8327M17.7083 9.99935C17.7083 14.2565 14.2572 17.7077 10 17.7077C5.74281 17.7077 2.29167 14.2565 2.29167 9.99935C2.29167 5.74215 5.74281 2.29102 10 2.29102C14.2572 2.29102 17.7083 5.74215 17.7083 9.99935Z"
            stroke="#868C98"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      ),
    },
    {
      id: "cancelled",
      label: "Cancelled",
      icon: () => (
        <svg
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.5 7.49935L7.5 12.4993M12.5 12.4993L7.5 7.49935M17.7083 9.99935C17.7083 14.2565 14.2572 17.7077 10 17.7077C5.7428 17.7077 2.29166 14.2565 2.29166 9.99935C2.29166 5.74215 5.7428 2.29102 10 2.29102C14.2572 2.29102 17.7083 5.74215 17.7083 9.99935Z"
            stroke="#868C98"
            stroke-width="1.5"
            stroke-linecap="round"
          />
        </svg>
      ),
    },
  ];

  const schema = yup.object({
    id: yup.string(),
    email: yup.string(),
    role: yup.string(),
    status: yup.string(),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  function previousPage() {
    getData(currentPage - 1, pageSize);
  }

  function nextPage() {
    getData(currentPage + 1, pageSize);
  }

  async function getData(pageNum, limitNum, data = {}, filters = []) {
    setLoading(showAddSidebar || showEditSidebar ? false : true);
    try {
      const response = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/user/reservations",
        {},
        "GET"
      );

      if (response) {
        setLoading(false);
        const currentDate = new Date();
        currentDate.setHours(0, 0, 0, 0); // Set to start of day for accurate comparison

        let filteredData = response.list;

        // Filter based on selected family member
        if (selectedFamilyMember && selectedFamilyMember.value !== "all") {
          if (selectedFamilyMember.value === "me") {
            // Show only current user's reservations
            const user_id = localStorage.getItem("user");
            filteredData = filteredData.filter((item) => {
              const playerIds = item.booking?.player_ids
                ? JSON.parse(item.booking.player_ids)
                : [];
              return (
                playerIds.includes(parseInt(user_id)) ||
                item.booking_user_id === parseInt(user_id)
              );
            });
          } else {
            // Show only selected family member's reservations
            filteredData = filteredData.filter((item) => {
              const playerIds = item.booking?.player_ids
                ? JSON.parse(item.booking.player_ids)
                : [];
              return (
                playerIds.includes(selectedFamilyMember.value.id) ||
                item.booking_user_id === selectedFamilyMember.value.id
              );
            });
          }
        }

        // Filter based on active tab
        if (activeTab === "upcoming") {
          filteredData = filteredData.filter((item) => {
            const bookingDate = new Date(item.booking_date);
            bookingDate.setHours(0, 0, 0, 0);
            return bookingDate >= currentDate;
          });
        } else if (activeTab === "past") {
          filteredData = filteredData.filter((item) => {
            const bookingDate = new Date(item.booking_date);
            bookingDate.setHours(0, 0, 0, 0);
            return bookingDate < currentDate;
          });
        } else if (activeTab === "cancelled") {
          filteredData = filteredData.filter(
            (item) => item.booking_status === 2
          );
        }

        setCurrentTableData(filteredData);
      }
    } catch (error) {
      setLoading(false);
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
  }

  const fetchSports = async () => {
    try {
      const user_id = localStorage.getItem("user");
      const userResponse2 = await tdk.getOne("user", user_id, {});
      const clubResponse = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/user/club/${userResponse2.model.club_id}`,
        {},
        "GET"
      );

      setSports(clubResponse.sports);
      // setSelectedPlayers((prev) => [...prev, userResponse]);
      setClub(clubResponse.model);
    } catch (error) {
      console.error(error);
    }
  };

  const fetchFamilyMembers = async () => {
    try {
      const user_id = localStorage.getItem("user");
      const familyResponse = await tdk.getList("user", {
        filter: [`guardian,eq,${user_id}`, `role,cs,user`],
      });
      setFamilyMembers(familyResponse.list);
    } catch (error) {
      console.error("Error fetching family members:", error);
    }
  };

  const fetchUserProfile = async () => {
    try {
      const user_id = localStorage.getItem("user");
      const userResponse = await tdk.getOne("user", user_id, {});
      setUserProfile(userResponse.model);
    } catch (error) {
      console.error("Error fetching user profile:", error);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "my-reservations",
      },
    });

    const delay = 700;
    const timeoutId = setTimeout(async () => {
      await getData(1, pageSize, {});
      await fetchSports();
      await fetchFamilyMembers();
      await fetchUserProfile();
    }, delay);

    return () => {
      clearTimeout(timeoutId);
    };
  }, []);

  // Add effect to refresh data when tab changes
  React.useEffect(() => {
    getData(1, pageSize, {});
  }, [activeTab]);

  // Add effect to refresh data when selected family member changes
  React.useEffect(() => {
    if (selectedFamilyMember !== null) {
      getData(1, pageSize, {});
    }
  }, [selectedFamilyMember]);

  const handleClickOutside = (event) => {
    if (
      dropdownFilterRef.current &&
      !dropdownFilterRef.current.contains(event.target)
    ) {
      setOpenFilter(false);
    }
  };

  React.useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleOpenDetails = (reservation) => {
    setSelectedReservation(reservation);
    setShowDetailsModal(true);
  };

  console.log("reservation data", data);

  // Create dropdown options for family member filter
  const familyMemberOptions = [
    { value: "all", label: "All Reservations" },
    { value: "me", label: "My Reservations" },
    ...familyMembers.map((member) => ({
      value: member,
      label: `${member.first_name} ${member.last_name} (${
        member.family_role || "Family Member"
      })`,
    })),
  ];

  return (
    <div>
      <div className="bg-white px-4 pt-4">
        <h1 className="mb-6 text-2xl font-semibold">My Reservations</h1>

        {/* Family Member Filter Dropdown */}
        {(familyMembers.length > 0 || userProfile) && (
          <div className="mb-6 max-w-sm">
            <label className="mb-2 block text-sm font-medium text-gray-900">
              Filter by family member
            </label>
            <Select
              className="w-full text-sm"
              options={familyMemberOptions}
              onChange={setSelectedFamilyMember}
              value={selectedFamilyMember}
              placeholder="Select family member"
              isSearchable={false}
              defaultValue={familyMemberOptions[0]} // Default to "All Reservations"
            />
          </div>
        )}

        {/* Tabs */}
        <div className="mb-0 flex max-w-fit  text-sm">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center gap-2 bg-transparent px-3 py-3 ${
                activeTab === tab.id ? "border-b-2 border-primaryBlue" : ""
              }`}
            >
              {tab.icon()}
              <span className="">{tab.label}</span>
            </button>
          ))}
        </div>
      </div>
      <div className="h-screen px-8">
        {loading && <LoadingOverlay />}

        {loading ? (
          <SkeletonLoader />
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full min-w-[1024px] ">
              <thead>
                <tr>
                  {columns.map((column, index) => (
                    <th
                      key={index}
                      scope="col"
                      className="px-6 py-4 text-left text-sm font-medium text-gray-500"
                    >
                      {column.header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y-8 divide-gray-50">
                {data.map((row, i) => {
                  const timeLeft = calculateReservationTimeLeft(
                    row?.reservation_updated_at
                  );
                  return (
                    <tr
                      key={i}
                      onClick={() => handleOpenDetails(row)}
                      className="hover:bg-gray-40 cursor-pointer rounded-lg bg-white px-4 py-3 text-gray-500"
                    >
                      {columns.map((cell, index) => {
                        if (cell.accessor == "") {
                          return (
                            <td
                              key={index}
                              className="whitespace-nowrap px-6 py-4"
                            >
                              <div className="flex items-center gap-3">
                                <button
                                  className="rounded-full p-2 hover:bg-gray-100"
                                  onClick={() => handleOpenDetails(row)}
                                >
                                  <svg
                                    width="20"
                                    height="20"
                                    viewBox="0 0 20 20"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <path
                                      d="M15.2083 12.7077V4.79102M15.2083 4.79102H7.29167M15.2083 4.79102L5 14.9993"
                                      stroke="#868C98"
                                      stroke-width="1.5"
                                      stroke-linecap="round"
                                      stroke-linejoin="round"
                                    />
                                  </svg>
                                </button>
                              </div>
                            </td>
                          );
                        }
                        if (
                          // cell.mapping &&
                          cell.accessor === "reservation_status"
                        ) {
                          return (
                            <td
                              key={index}
                              className="flex gap-2 whitespace-nowrap px-6 py-5 text-sm"
                            >
                              {row?.booking_type == "Find Buddy" && (
                                <>
                                  {row?.num_needed == row?.num_players && (
                                    <GroupFullStatus title={"Group full"} />
                                  )}

                                  {row?.num_needed != row?.num_players && (
                                    <LookingForBuddiesStatus
                                      numberOfBuddies={row?.num_needed}
                                    />
                                  )}
                                </>
                              )}
                              {(row?.booking_status ===
                                BOOKING_STATUSES.PENDING && (
                                <div className="flex items-center gap-2">
                                  <ReservedStatus />
                                  <TimeStatus timeLeft={timeLeft} />
                                </div>
                              )) ||
                                (row?.booking_status ===
                                  BOOKING_STATUSES.SUCCESS && <PaidStatus />) ||
                                (row?.booking_status ===
                                  BOOKING_STATUSES.FAIL && <FailedStatus />)}
                            </td>
                          );
                        }
                        if (cell.mappingExist) {
                          return (
                            <td
                              key={index}
                              className="whitespace-nowrap px-6 py-4"
                            >
                              {cell.mappings[row[cell.accessor]]}
                            </td>
                          );
                        }
                        if (cell.accessor === "type") {
                          return (
                            <td
                              key={index}
                              className="whitespace-nowrap px-6 py-4 capitalize"
                            >
                              {
                                eventTypeOptions.find(
                                  (option) => option.value === row?.type
                                )?.label
                              }
                            </td>
                          );
                        }

                        if (cell.accessor === "date") {
                          return (
                            <td
                              key={index}
                              className="whitespace-nowrap rounded-l-3xl px-6 py-4"
                            >
                              {fDate(row?.booking_date || "") || "--"} {" | "}{" "}
                              {fTimeSuffix(row?.start_time || "") || "--"}{" "}
                              {" - "} {fTimeSuffix(row?.end_time || "") || "--"}
                            </td>
                          );
                        }
                        if (cell.accessor === "booking_type") {
                          return (
                            <td
                              key={index}
                              className="whitespace-nowrap px-6 py-4 capitalize"
                            >
                              {row?.booking_type || "--"}
                            </td>
                          );
                        }
                        if (cell.accessor === "players") {
                          return (
                            <td
                              key={index}
                              className="whitespace-nowrap px-6 py-4"
                            >
                              {row?.booking?.player_ids
                                ? `${
                                    JSON.parse(row?.booking?.player_ids).length
                                  } players`
                                : "0 players"}
                            </td>
                          );
                        }
                        if (cell.accessor === "price") {
                          return (
                            <td
                              key={index}
                              className="whitespace-nowrap px-6 py-4"
                            >
                              {fCurrency(row?.price || 0)}
                            </td>
                          );
                        }
                        if (cell.accessor === "user") {
                          return (
                            <td
                              key={index}
                              className="whitespace-nowrap px-6 py-4"
                            >
                              {!row?.user?.first_name || !row?.user?.last_name
                                ? "--"
                                : `${row?.user?.first_name} ${row?.user?.last_name}`}
                            </td>
                          );
                        }
                        return (
                          <td
                            key={index}
                            className="whitespace-nowrap px-6 py-4 capitalize"
                          >
                            {row[cell.accessor]}
                          </td>
                        );
                      })}
                    </tr>
                  );
                })}
              </tbody>
            </table>
            {!loading && data.length === 0 && (
              <div className="w-full px-6 py-4 text-center">
                <p className="text-gray-500">No data available</p>
              </div>
            )}
          </div>
        )}
        <PaginationBar
          currentPage={currentPage}
          pageCount={pageCount}
          pageSize={pageSize}
          canPreviousPage={canPreviousPage}
          canNextPage={canNextPage}
          updatePageSize={(newPageSize) => {
            setPageSize(newPageSize);
            getData(1, newPageSize);
          }}
          previousPage={previousPage}
          nextPage={nextPage}
          gotoPage={(pageNum) => getData(pageNum, pageSize)}
        />

        <ReservationDetailModal
          isOpen={!!selectedReservation}
          onClose={() => setSelectedReservation(null)}
          reservation={selectedReservation}
          clubSports={sports}
          club={club}
        />
      </div>{" "}
    </div>
  );
};

export default UserMyReservation;
